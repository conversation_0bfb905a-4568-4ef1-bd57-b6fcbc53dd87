<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统监控 - 服务器监控系统</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- 自定义样式 -->
    <style>
        .metric-card {
            transition: all 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-2px);
        }
        
        .progress-bar {
            transition: width 0.5s ease-in-out;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .animate-pulse-custom {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-white via-gray-50 to-gray-100 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-xl font-bold text-gray-900">服务器监控系统</h1>
                    </div>
                    <div class="hidden md:ml-6 md:flex md:space-x-8">
                        <a href="example.html" class="text-gray-500 hover:text-blue-600 px-3 py-2 text-sm font-medium">仪表板</a>
                        <a href="services-example.html" class="text-gray-500 hover:text-blue-600 px-3 py-2 text-sm font-medium">服务管理</a>
                        <a href="#" class="text-gray-900 hover:text-blue-600 px-3 py-2 text-sm font-medium">系统监控</a>

                    </div>
                </div>
                <div class="flex items-center">
                    <span class="text-sm text-gray-500" id="current-time"></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            <!-- 页面标题 -->
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-gray-900">系统监控</h2>
                <p class="mt-2 text-gray-600">实时监控系统资源使用情况</p>
            </div>

            <!-- 系统信息概览 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- CPU 使用率 -->
                <div class="metric-card bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">CPU 使用率</h3>
                        <div class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="mb-2">
                        <span class="text-3xl font-bold text-gray-900" id="cpu-usage">37.90%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                        <div class="bg-gradient-to-r from-blue-400 to-blue-600 h-2 rounded-full progress-bar" id="cpu-bar" style="width: 37.9%"></div>
                    </div>
                    <p class="text-sm text-gray-600">4 核心 @ 2.4GHz</p>
                </div>

                <!-- 内存使用率 -->
                <div class="metric-card bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">内存使用率</h3>
                        <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="mb-2">
                        <span class="text-3xl font-bold text-gray-900" id="memory-usage">53.65%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                        <div class="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full progress-bar" id="memory-bar" style="width: 53.65%"></div>
                    </div>
                    <p class="text-sm text-gray-600">4.3GB / 8GB</p>
                </div>

                <!-- 磁盘使用率 -->
                <div class="metric-card bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">磁盘使用率</h3>
                        <div class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="mb-2">
                        <span class="text-3xl font-bold text-gray-900" id="disk-usage">68.42%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                        <div class="bg-gradient-to-r from-yellow-400 to-yellow-600 h-2 rounded-full progress-bar" id="disk-bar" style="width: 68.42%"></div>
                    </div>
                    <p class="text-sm text-gray-600">137GB / 200GB</p>
                </div>

                <!-- 网络流量 -->
                <div class="metric-card bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">网络流量</h3>
                        <div class="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="mb-2">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">↑ <span id="upload-speed">2.3 MB/s</span></span>
                            <span class="text-gray-600">↓ <span id="download-speed">5.7 MB/s</span></span>
                        </div>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                        <div class="bg-gradient-to-r from-purple-400 to-purple-600 h-2 rounded-full progress-bar" id="network-bar" style="width: 45%"></div>
                    </div>
                    <p class="text-sm text-gray-600">1Gbps 网卡</p>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- CPU 历史图表 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">CPU 使用率历史</h3>
                    <div class="relative h-64">
                        <canvas id="cpu-chart"></canvas>
                    </div>
                </div>

                <!-- 内存历史图表 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">内存使用率历史</h3>
                    <div class="relative h-64">
                        <canvas id="memory-chart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 系统信息详情 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 系统信息 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">系统信息</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">操作系统:</span>
                            <span class="font-medium">Ubuntu 20.04.3 LTS</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">内核版本:</span>
                            <span class="font-medium">5.4.0-88-generic</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">运行时间:</span>
                            <span class="font-medium" id="system-uptime">15天 6小时 32分钟</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">负载平均:</span>
                            <span class="font-medium">0.85, 0.92, 1.05</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">进程数:</span>
                            <span class="font-medium">156</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">用户数:</span>
                            <span class="font-medium">3</span>
                        </div>
                    </div>
                </div>

                <!-- 磁盘信息 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">磁盘信息</h3>
                    <div class="space-y-4">
                        <div class="border-b border-gray-200 pb-3">
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-medium">/dev/sda1 (/)</span>
                                <span class="text-sm text-gray-600">137GB / 200GB</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-gradient-to-r from-blue-400 to-blue-600 h-2 rounded-full" style="width: 68.5%"></div>
                            </div>
                        </div>
                        <div class="border-b border-gray-200 pb-3">
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-medium">/dev/sdb1 (/home)</span>
                                <span class="text-sm text-gray-600">245GB / 500GB</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full" style="width: 49%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-medium">/dev/sdc1 (/var)</span>
                                <span class="text-sm text-gray-600">89GB / 100GB</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-gradient-to-r from-yellow-400 to-yellow-600 h-2 rounded-full" style="width: 89%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 系统监控数据
        let systemData = {
            cpu: 37.90,
            memory: 53.65,
            disk: 68.42,
            network: { upload: 2.3, download: 5.7 }
        };

        // 历史数据
        let cpuHistory = [];
        let memoryHistory = [];
        let timeLabels = [];

        // 图表实例
        let cpuChart, memoryChart;

        // 初始化图表
        function initCharts() {
            // CPU 图表
            const cpuCtx = document.getElementById('cpu-chart').getContext('2d');
            cpuChart = new Chart(cpuCtx, {
                type: 'line',
                data: {
                    labels: timeLabels,
                    datasets: [{
                        label: 'CPU 使用率 (%)',
                        data: cpuHistory,
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    scales: {
                        x: {
                            display: true,
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: 'white',
                            bodyColor: 'white'
                        }
                    }
                }
            });

            // 内存图表
            const memoryCtx = document.getElementById('memory-chart').getContext('2d');
            memoryChart = new Chart(memoryCtx, {
                type: 'line',
                data: {
                    labels: timeLabels,
                    datasets: [{
                        label: '内存使用率 (%)',
                        data: memoryHistory,
                        borderColor: 'rgb(34, 197, 94)',
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    scales: {
                        x: {
                            display: true,
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: 'white',
                            bodyColor: 'white'
                        }
                    }
                }
            });

            // 初始化历史数据
            for (let i = 0; i < 20; i++) {
                const time = new Date(Date.now() - (19 - i) * 5000);
                timeLabels.push(time.toLocaleTimeString());
                cpuHistory.push(Math.random() * 40 + 20);
                memoryHistory.push(Math.random() * 30 + 40);
            }

            cpuChart.update();
            memoryChart.update();
        }

        // 更新系统数据
        function updateSystemData() {
            // 模拟数据变化
            systemData.cpu += (Math.random() - 0.5) * 5;
            systemData.memory += (Math.random() - 0.5) * 3;
            systemData.disk += (Math.random() - 0.5) * 0.5;
            systemData.network.upload = Math.random() * 5 + 1;
            systemData.network.download = Math.random() * 10 + 2;

            // 限制范围
            systemData.cpu = Math.max(5, Math.min(95, systemData.cpu));
            systemData.memory = Math.max(20, Math.min(90, systemData.memory));
            systemData.disk = Math.max(60, Math.min(95, systemData.disk));

            // 更新显示
            document.getElementById('cpu-usage').textContent = systemData.cpu.toFixed(2) + '%';
            document.getElementById('memory-usage').textContent = systemData.memory.toFixed(2) + '%';
            document.getElementById('disk-usage').textContent = systemData.disk.toFixed(2) + '%';
            document.getElementById('upload-speed').textContent = systemData.network.upload.toFixed(1) + ' MB/s';
            document.getElementById('download-speed').textContent = systemData.network.download.toFixed(1) + ' MB/s';

            // 更新进度条
            document.getElementById('cpu-bar').style.width = systemData.cpu + '%';
            document.getElementById('memory-bar').style.width = systemData.memory + '%';
            document.getElementById('disk-bar').style.width = systemData.disk + '%';
            document.getElementById('network-bar').style.width = (systemData.network.download / 10 * 100) + '%';

            // 更新图表
            const now = new Date();
            timeLabels.push(now.toLocaleTimeString());
            cpuHistory.push(systemData.cpu);
            memoryHistory.push(systemData.memory);

            // 保持最多20个数据点
            if (timeLabels.length > 20) {
                timeLabels.shift();
                cpuHistory.shift();
                memoryHistory.shift();
            }

            cpuChart.update('none');
            memoryChart.update('none');
        }

        // 更新时间
        function updateTime() {
            document.getElementById('current-time').textContent = new Date().toLocaleString();
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('System monitoring page loaded');
            
            updateTime();
            initCharts();
            
            // 每3秒更新系统数据
            setInterval(updateSystemData, 3000);
            
            // 每秒更新时间
            setInterval(updateTime, 1000);
        });
    </script>
</body>
</html>
