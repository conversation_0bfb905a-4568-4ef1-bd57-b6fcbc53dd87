<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>服务管理 - 服务器监控系统</title>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- 自定义样式 -->
    <style>
      .service-card {
        transition: all 0.3s ease;
      }

      .service-card:hover {
        transform: translateY(-2px);
      }

      .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
      }

      .status-running {
        background-color: #10b981;
        box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
      }

      .status-stopped {
        background-color: #ef4444;
        box-shadow: 0 0 8px rgba(239, 68, 68, 0.4);
      }

      .status-failed {
        background-color: #f59e0b;
        box-shadow: 0 0 8px rgba(245, 158, 11, 0.4);
      }

      @keyframes spin {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }

      .animate-spin-custom {
        animation: spin 1s linear infinite;
      }
    </style>
  </head>
  <body
    class="bg-gradient-to-br from-white via-gray-50 to-gray-100 min-h-screen"
  >
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <h1 class="text-xl font-bold text-gray-900">服务器监控系统</h1>
            </div>
            <div class="hidden md:ml-6 md:flex md:space-x-8">
              <a
                href="example.html"
                class="text-gray-500 hover:text-blue-600 px-3 py-2 text-sm font-medium"
                >仪表板</a
              >
              <a
                href="#"
                class="text-gray-900 hover:text-blue-600 px-3 py-2 text-sm font-medium"
                >服务管理</a
              >
              <a
                href="system-example.html"
                class="text-gray-500 hover:text-blue-600 px-3 py-2 text-sm font-medium"
                >系统监控</a
              >
            </div>
          </div>
          <div class="flex items-center">
            <span class="text-sm text-gray-500" id="current-time"></span>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <!-- 页面标题 -->
        <div class="mb-8">
          <div
            class="flex flex-col md:flex-row md:items-center md:justify-between"
          >
            <div>
              <h2 class="text-3xl font-bold text-gray-900">服务管理</h2>
              <p class="mt-2 text-gray-600">
                管理 Supervisor、Systemd、Docker 服务
              </p>
            </div>
            <div class="mt-4 md:mt-0 flex space-x-3">
              <!-- 服务类型过滤 -->
              <select
                id="service-type-filter"
                class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">所有服务</option>
                <option value="supervisor">Supervisor</option>
                <option value="systemd">Systemd</option>
                <option value="docker">Docker</option>
              </select>
              <!-- 状态过滤 -->
              <select
                id="status-filter"
                class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">所有状态</option>
                <option value="running">运行中</option>
                <option value="stopped">已停止</option>
                <option value="failed">失败</option>
              </select>
              <!-- 刷新按钮 -->
              <button
                onclick="refreshServices()"
                class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                <svg
                  class="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  ></path>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- 统计概览 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div
                  class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center"
                >
                  <svg
                    class="w-5 h-5 text-green-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">运行中</p>
                <p class="text-2xl font-bold text-gray-900" id="running-count">
                  15
                </p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div
                  class="w-8 h-8 bg-red-100 rounded-md flex items-center justify-center"
                >
                  <svg
                    class="w-5 h-5 text-red-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">已停止</p>
                <p class="text-2xl font-bold text-gray-900" id="stopped-count">
                  3
                </p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div
                  class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center"
                >
                  <svg
                    class="w-5 h-5 text-yellow-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">失败</p>
                <p class="text-2xl font-bold text-gray-900" id="failed-count">
                  2
                </p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div
                  class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center"
                >
                  <svg
                    class="w-5 h-5 text-blue-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                    ></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">总服务</p>
                <p class="text-2xl font-bold text-gray-900">20</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 服务管理标签页 -->
        <div class="bg-white rounded-lg shadow-md">
          <!-- 标签页头部 -->
          <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
              <button
                onclick="switchTab('supervisor')"
                class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                data-tab="supervisor"
              >
                Supervisor 服务
              </button>
              <button
                onclick="switchTab('systemd')"
                class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                data-tab="systemd"
              >
                Systemd 服务
              </button>
              <button
                onclick="switchTab('docker')"
                class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                data-tab="docker"
              >
                Docker 容器
              </button>
            </nav>
          </div>

          <!-- 标签页内容 -->
          <div class="p-6">
            <!-- Supervisor 服务 -->
            <div id="supervisor-tab" class="tab-content">
              <div
                class="grid grid-cols-1 lg:grid-cols-2 gap-6"
                id="supervisor-services"
              >
                <!-- 服务卡片将通过JavaScript动态生成 -->
              </div>
            </div>

            <!-- Systemd 服务 -->
            <div id="systemd-tab" class="tab-content hidden">
              <div
                class="grid grid-cols-1 lg:grid-cols-2 gap-6"
                id="systemd-services"
              >
                <!-- 服务卡片将通过JavaScript动态生成 -->
              </div>
            </div>

            <!-- Docker 容器 -->
            <div id="docker-tab" class="tab-content hidden">
              <div
                class="grid grid-cols-1 lg:grid-cols-2 gap-6"
                id="docker-services"
              >
                <!-- 服务卡片将通过JavaScript动态生成 -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <script>
      // 服务数据
      const servicesData = {
        supervisor: [
          {
            name: 'nginx',
            status: 'running',
            pid: 1234,
            uptime: '2d 3h',
            memory: '45MB',
            cpu: '2.3%',
            description: 'Nginx Web Server',
          },
          {
            name: 'gunicorn',
            status: 'running',
            pid: 1235,
            uptime: '1d 12h',
            memory: '128MB',
            cpu: '5.7%',
            description: 'Python WSGI Server',
          },
          {
            name: 'celery-worker',
            status: 'running',
            pid: 1236,
            uptime: '3h 45m',
            memory: '89MB',
            cpu: '3.2%',
            description: 'Celery Task Worker',
          },
          {
            name: 'redis-server',
            status: 'stopped',
            pid: null,
            uptime: '--',
            memory: '--',
            cpu: '--',
            description: 'Redis Cache Server',
          },
          {
            name: 'elasticsearch',
            status: 'failed',
            pid: null,
            uptime: '--',
            memory: '--',
            cpu: '--',
            description: 'Elasticsearch Search Engine',
          },
        ],
        systemd: [
          {
            name: 'apache2',
            status: 'running',
            pid: 2234,
            uptime: '5d 8h',
            memory: '67MB',
            cpu: '1.8%',
            description: 'Apache HTTP Server',
          },
          {
            name: 'mysql',
            status: 'running',
            pid: 2235,
            uptime: '10d 2h',
            memory: '256MB',
            cpu: '4.5%',
            description: 'MySQL Database Server',
          },
          {
            name: 'postgresql',
            status: 'running',
            pid: 2236,
            uptime: '7d 15h',
            memory: '189MB',
            cpu: '3.1%',
            description: 'PostgreSQL Database',
          },
          {
            name: 'ssh',
            status: 'running',
            pid: 2237,
            uptime: '15d 6h',
            memory: '12MB',
            cpu: '0.1%',
            description: 'SSH Daemon',
          },
          {
            name: 'cron',
            status: 'running',
            pid: 2238,
            uptime: '15d 6h',
            memory: '8MB',
            cpu: '0.0%',
            description: 'Cron Scheduler',
          },
          {
            name: 'fail2ban',
            status: 'stopped',
            pid: null,
            uptime: '--',
            memory: '--',
            cpu: '--',
            description: 'Intrusion Prevention',
          },
        ],
        docker: [
          {
            name: 'web-app',
            status: 'running',
            pid: 3234,
            uptime: '1d 8h',
            memory: '512MB',
            cpu: '8.2%',
            description: 'Main Web Application',
          },
          {
            name: 'database',
            status: 'running',
            pid: 3235,
            uptime: '3d 12h',
            memory: '1.2GB',
            cpu: '6.7%',
            description: 'Database Container',
          },
          {
            name: 'cache',
            status: 'running',
            pid: 3236,
            uptime: '2d 4h',
            memory: '256MB',
            cpu: '2.1%',
            description: 'Redis Cache Container',
          },
          {
            name: 'monitoring',
            status: 'running',
            pid: 3237,
            uptime: '5d 1h',
            memory: '128MB',
            cpu: '1.5%',
            description: 'Monitoring Stack',
          },
          {
            name: 'backup',
            status: 'stopped',
            pid: null,
            uptime: '--',
            memory: '--',
            cpu: '--',
            description: 'Backup Service',
          },
          {
            name: 'test-env',
            status: 'failed',
            pid: null,
            uptime: '--',
            memory: '--',
            cpu: '--',
            description: 'Test Environment',
          },
        ],
      };

      let currentTab = 'supervisor';

      // 生成服务卡片HTML
      function createServiceCard(service, type) {
        const statusClass =
          service.status === 'running'
            ? 'status-running'
            : service.status === 'stopped'
            ? 'status-stopped'
            : 'status-failed';

        const statusText =
          service.status === 'running'
            ? '运行中'
            : service.status === 'stopped'
            ? '已停止'
            : '失败';

        const statusTextClass =
          service.status === 'running'
            ? 'text-green-600'
            : service.status === 'stopped'
            ? 'text-red-600'
            : 'text-yellow-600';

        const actionButtons =
          service.status === 'running'
            ? `<button onclick="stopService('${service.name}', '${type}')" class="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600 transition-colors">停止</button>
                 <button onclick="restartService('${service.name}', '${type}')" class="px-3 py-1 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600 transition-colors">重启</button>`
            : `<button onclick="startService('${service.name}', '${type}')" class="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600 transition-colors">启动</button>`;

        return `
                <div class="service-card bg-gray-50 rounded-lg p-4 border border-gray-200 hover:shadow-md transition-all duration-300">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <span class="status-indicator ${statusClass}"></span>
                            <h3 class="text-lg font-semibold text-gray-900">${
                              service.name
                            }</h3>
                        </div>
                        <span class="text-sm font-medium ${statusTextClass}">${statusText}</span>
                    </div>

                    <p class="text-sm text-gray-600 mb-3">${
                      service.description
                    }</p>

                    <div class="grid grid-cols-2 gap-4 mb-3 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">PID:</span>
                            <span class="text-gray-600">${
                              service.pid || '--'
                            }</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">运行时间:</span>
                            <span class="text-gray-600">${service.uptime}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">内存:</span>
                            <span class="text-gray-600">${service.memory}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">CPU:</span>
                            <span class="text-gray-600">${service.cpu}</span>
                        </div>
                    </div>

                    <div class="flex space-x-2">
                        ${actionButtons}
                        <button onclick="viewLogs('${
                          service.name
                        }', '${type}')" class="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors">日志</button>
                    </div>
                </div>
            `;
      }

      // 渲染服务列表
      function renderServices(type = currentTab) {
        const container = document.getElementById(`${type}-services`);
        const services = servicesData[type] || [];
        container.innerHTML = services
          .map(service => createServiceCard(service, type))
          .join('');
      }

      // 切换标签页
      function switchTab(tab) {
        // 隐藏所有标签页内容
        document.querySelectorAll('.tab-content').forEach(content => {
          content.classList.add('hidden');
        });

        // 重置所有标签按钮样式
        document.querySelectorAll('.tab-button').forEach(button => {
          button.classList.remove('border-blue-500', 'text-blue-600');
          button.classList.add('border-transparent', 'text-gray-500');
        });

        // 显示选中的标签页
        document.getElementById(`${tab}-tab`).classList.remove('hidden');

        // 激活选中的标签按钮
        const activeButton = document.querySelector(`[data-tab="${tab}"]`);
        activeButton.classList.remove('border-transparent', 'text-gray-500');
        activeButton.classList.add('border-blue-500', 'text-blue-600');

        currentTab = tab;
        renderServices(tab);
      }

      // 服务操作函数
      function startService(name, type) {
        alert(`启动 ${type} 服务: ${name}`);
        // 这里可以发送API请求
      }

      function stopService(name, type) {
        alert(`停止 ${type} 服务: ${name}`);
        // 这里可以发送API请求
      }

      function restartService(name, type) {
        alert(`重启 ${type} 服务: ${name}`);
        // 这里可以发送API请求
      }

      function viewLogs(name, type) {
        alert(`查看 ${type} 服务日志: ${name}`);
        // 这里可以打开日志查看器
      }

      function refreshServices() {
        const button = event.target.closest('button');
        const svg = button.querySelector('svg');
        svg.classList.add('animate-spin-custom');

        setTimeout(() => {
          svg.classList.remove('animate-spin-custom');
          renderServices();
        }, 1000);
      }

      // 更新时间
      function updateTime() {
        document.getElementById('current-time').textContent =
          new Date().toLocaleString();
      }

      // 页面初始化
      document.addEventListener('DOMContentLoaded', function () {
        console.log('Services management page loaded');

        switchTab('supervisor'); // 默认显示supervisor标签
        updateTime();

        // 每秒更新时间
        setInterval(updateTime, 1000);
      });
    </script>
  </body>
</html>
